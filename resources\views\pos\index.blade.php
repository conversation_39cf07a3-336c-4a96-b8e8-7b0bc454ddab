@php
    // $logo=asset(Storage::url('uploads/logo/'));
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $company_favicon = Utility::getValByName('company_favicon');
    $SITE_RTL = Utility::getValByName('SITE_RTL');
    $setting = \App\Models\Utility::colorset();
    $color = 'theme-3';
    if (!empty($setting['color'])) {
        $color = $setting['color'];
    }

    if (isset($setting['color_flag']) && $setting['color_flag'] == 'true') {
        $themeColor = 'theme-3';
    } else {
        $themeColor = $color;
    }

@endphp
<!DOCTYPE html>

<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>
        {{ !empty($companySettings['header_text']) ? $companySettings['header_text']->value : config('app.name', 'ERPGO SaaS') }}
        - {{ __('POS') }}</title>

    <link rel="icon"
        href="{{ asset(Storage::url('uploads/logo/')) . '/' . (isset($companySettings['company_favicon']) && !empty($companySettings['company_favicon']) ? $companySettings['company_favicon']->value : 'favicon.png') }}"
        type="image" sizes="16x16">
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/site.css') }}" id="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- font css -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">

    <!--bootstrap switch-->
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/bootstrap-switch-button.min.css') }}">

    <!-- vendor css -->
    @if ($SITE_RTL == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style-rtl.css') }}">
    @endif
    @if ($setting['cust_darklayout'] == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style-dark.css') }}">
    @else
        <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" id="main-style-link">
    @endif

    <link rel="stylesheet" href="{{ asset('assets/css/customizer.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}" id="main-style-link">

    <style>
        .bg-color {
            @if ($color == 'theme-1')
                background: linear-gradient(141.55deg, rgba(81, 69, 157, 0) 3.46%, rgba(255, 58, 110, 0.6) 99.86%), #51459d;
            @elseif($color == 'theme-2')
                background: linear-gradient(141.55deg, rgba(81, 69, 157, 0) 3.46%, #4ebbd3 99.86%), #1f3996;
            @elseif($color == 'theme-3')
                background: linear-gradient(141.55deg,rgb(0, 0, 0) 3.46%, #000000 99.86%), #000000; /* تم تغيير اللون من #6fd943 إلى #000000 */
            @elseif($color == 'theme-4')
                background: linear-gradient(141.55deg, rgba(104, 94, 229, 0) 3.46%, #685ee5 99.86%), #584ed2;
            @endif
        }

        /* تحسين مظهر زر الطباعة الحرارية */
        #thermal-print-section {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .thermal-print-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 350px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        #thermal-print-btn {
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #thermal-print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            background: linear-gradient(45deg, #20c997, #28a745);
        }

        /* تحسين مظهر الأزرار في المودال */
        .modal-footer .btn {
            transition: all 0.3s ease;
        }

        .modal-footer .btn:hover {
            transform: translateY(-1px);
        }

        /* تحسين مظهر زر الطباعة الحرارية في المودال */
        #thermal-print-modal {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            transition: all 0.3s ease;
        }

        #thermal-print-modal:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* تأثير النبضة للزر */
        .btn-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }

        /* تحسين مظهر التنبيه */
        .thermal-print-alert {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95) !important;
        }

        /* ========== NEW FEATURE: Print Invoice Button Styles ========== */

        /* تحسين مظهر زر "طباعة فاتورة" */
        #print-invoice-btn {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
            transition: all 0.3s ease;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(23, 162, 184, 0.4);
            animation: printButtonGlow 3s ease-in-out infinite alternate;
            min-width: 140px;
            font-size: 0.9em;
        }

        #print-invoice-btn:hover {
            background: linear-gradient(45deg, #138496, #17a2b8);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
        }

        #print-invoice-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        #print-invoice-btn:hover::before {
            left: 100%;
        }

        /* تأثير الوهج للزر */
        @keyframes printButtonGlow {
            0% {
                box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
            }
            100% {
                box-shadow: 0 2px 15px rgba(23, 162, 184, 0.6);
            }
        }

        /* تحسين مظهر النافذة المنبثقة لاختيار الفاتورة */
        #invoiceSelectionModal .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        #invoiceSelectionModal .modal-header {
            border-radius: 15px 15px 0 0;
            background: linear-gradient(45deg, #17a2b8, #138496) !important;
        }

        #invoiceSelectionModal .table {
            border-radius: 10px;
            overflow: hidden;
        }

        #invoiceSelectionModal .table thead th {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        #invoiceSelectionModal .table tbody tr {
            transition: all 0.3s ease;
        }

        #invoiceSelectionModal .table tbody tr:hover {
            background-color: rgba(23, 162, 184, 0.1);
            transform: scale(1.01);
        }

        /* تحسين أزرار الطباعة في الجدول */
        .thermal-print-invoice {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .thermal-print-invoice:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4);
        }

        /* تحسين مؤشر التحميل */
        #invoices-loading .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* تحسين حقل البحث */
        #invoice-search {
            border-radius: 25px;
            border: 2px solid #17a2b8;
            transition: all 0.3s ease;
        }

        #invoice-search:focus {
            border-color: #138496;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }

        /* تحسين الشارات */
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 15px;
        }

        /* تأثيرات إضافية للنافذة المنبثقة */
        #invoiceSelectionModal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
            transform: translate(0, -50px);
        }

        #invoiceSelectionModal.show .modal-dialog {
            transform: none;
        }

        /* تحسين أزرار النافذة المنبثقة */
        #invoiceSelectionModal .modal-footer .btn {
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        #invoiceSelectionModal .modal-footer .btn:hover {
            transform: translateY(-2px);
        }

        #refresh-invoices {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
        }

        #refresh-invoices:hover {
            background: linear-gradient(45deg, #138496, #17a2b8);
        }

        /* تحسين مظهر معلومات الشفت */
        #shift-info-alert {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            border: none;
            color: white;
            border-radius: 10px;
        }

        #shift-info-alert .ti-clock {
            font-size: 1.2em;
        }

        #shift-details strong {
            color: #fff;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
    </style>

    @stack('css-page')
</head>

<body class="{{ $themeColor }}">
    <div class="container-fluid px-2">
        <?php $lastsegment = request()->segment(count(request()->segments())); ?>
        <div class="row">
            <div class="col-12">
                <div class="mt-2 pos-top-bar bg-color d-flex justify-content-between bg-primary">
                    <span class="text-white">{{ __('POS') }}</span>
                    <a href="{{ route('dashboard') }}" class="text-white"><i class="ti ti-home"
                            style="font-size: 20px;"></i> </a>
                </div>
            </div>
        </div>
        <div class="mt-2 row">
            <div class="col-lg-7">
                <div class="sop-card card">
                    <div class="card-header p-2">
                        <div class="search-bar-left">
                            <form>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="ti ti-barcode"></i></span>
                                            </div>
                                            <input type="text" data-url="{{ route('search.products') }}"
                                                placeholder="{{ __('Scan Barcode or Enter SKU - Product will be added to cart automatically') }}"
                                                class="form-control pr-4 rounded-right barcodeScanner"
                                                autocomplete="off"
                                                autofocus>
                                            <div class="input-group-append">
                                                <span class="input-group-text bg-success text-white">
                                                    <i class="ti ti-shopping-cart"></i>
                                                </span>
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ __('1. Select warehouse → 2. Scan products → 3. Select customer → 4. Pay') }}</small>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <div class="right-content">
                            <div class="button-list b-bottom catgory-pad">
                                <div class="form-row m-0" id="categories-listing">
                                </div>
                            </div>
                            <div class="product-body-nop">
                                <div class="form-row" id="product-listing">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 ps-lg-0">
                <div class="card m-0">
                    <div class="card-header p-2">
                        <div class="row">
                            <div class="col-md-6">
                                {{ Form::select('customer_id', $customers, $customer, ['class' => 'form-control select customer_select', 'id' => 'customer', 'required' => 'required', 'placeholder' => __('Select Customer') ]) }}
                                {{ Form::hidden('vc_name_hidden', '', ['id' => 'vc_name_hidden']) }}

                            </div>
                            <div class="col-md-6">
                                {{ Form::select('warehouse_id', $warehouses, $warehouseId, ['class' => 'form-control select warehouse_select ', 'id' => 'warehouse', 'required' => 'required', 'placeholder' => __('Select Warehouse') ]) }}
                                {{ Form::hidden('warehouse_name_hidden', '', ['id' => 'warehouse_name_hidden']) }}
                                {{ Form::hidden('quotation_id', $id, ['id' => 'quotation_id', 'class' => 'quotation']) }}
                            </div>
                        </div>
                    </div>
                    <div class="card-header p-2" id="user-section" style="display: none;">
                        <div class="row">
                            <div class="col-md-12">
                                {{ Form::select('user_id', $deliveryUsers, null, ['class' => 'form-control select user_select ', 'id' => 'user',  'placeholder' => __('Select User') ]) }}
                                {{ Form::hidden('delivery_user_hidden', '', ['id' => 'delivery_user_hidden']) }}
                            </div>
                        </div>
                    </div>
                    <div class="card-body carttable cart-product-list carttable-scroll" id="carthtml">
                        @php
                            $total = 0;
                            $netPrice = 0;
                            $discount = 0;
                        @endphp
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th class="text-left">{{ __('Name') }}</th>
                                        <th class="text-center">{{ __('QTY') }}</th>
                                        <th>{{ __('Tax') }}</th>
                                        <th class="text-center">{{ __('Price') }}</th>
                                        <th class="text-center">{{ __('Sub Total') }}</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody id="tbody">
                                    @if (session($lastsegment) && !empty(session($lastsegment)) && count(session($lastsegment)) > 0)
                                        @foreach (session($lastsegment) as $id => $details)
                                            @php
                                                $product = \App\Models\ProductService::find($details['id']);
                                                $image_url =
                                                    !empty($product) && isset($product->pro_image)
                                                        ? $product->pro_image
                                                        : 'uploads/pro_image/';
                                                $total += $details['subtotal'];
                                                $netPrice += $details['netPrice'];
                                                $discount += $details['discount'];
                                            @endphp
                                            <tr data-product-id="{{ $id }}"
                                                id="product-id-{{ $id }}">
                                                <td class="cart-images">
                                                    <img alt="Image placeholder"
                                                        src="{{ asset(Storage::url('uploads/pro_image/' . $image_url)) }}"
                                                        class="card-image avatar rounded-circle-sale shadow hover-shadow-lg">
                                                </td>
                                                <td class="name">{{ $details['name'] }}</td>
                                                <td>
                                                    <span class="quantity buttons_added">
                                                        <input type="button" value="-" class="minus">
                                                        <input type="number" step="1" min="1"
                                                            max="" name="quantity"
                                                            title="{{ __('Quantity') }}" class="input-number"
                                                            data-url="{{ url('update-cart/') }}"
                                                            data-id="{{ $id }}" size="4"
                                                            value="{{ $details['quantity'] }}" placeholder="{{ __('Enter Quantity') }}">
                                                        <input type="button" value="+" class="plus">
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (!empty($product->tax_id))
                                                        @php
                                                            $taxes = \Utility::tax($product->tax_id);
                                                        @endphp
                                                        @foreach ($taxes as $tax)
                                                            <span
                                                                class="badge bg-primary">{{ $tax->name . ' (' . $tax->rate . '%)' }}</span>
                                                            <br>
                                                        @endforeach
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                                <td class="price text-right">
                                                    {{ Auth::user()->priceFormat($details['price']) }}</td>
                                                <td class="col-sm-3 mt-2">
                                                    <span
                                                        class="subtotal">{{ Auth::user()->priceFormat($details['subtotal']) }}</span>
                                                </td>
                                                <td class="col-sm-2 mt-2">
                                                <div class="action-btn">
                                                    <a href="#" class="btn btn-sm bg-danger bs-pass-para-pos"
                                                        data-confirm="{{ __('Are You Sure?') }}"
                                                        data-text="{{ __('This action can not be undone. Do you want to continue?') }}"
                                                        data-confirm-yes="delete-form-{{ $id }}"
                                                        title="{{ __('Delete') }}" data-id="{{ $id }}">
                                                        <i class="ti ti-trash text-white "
                                                            title="{{ __('Delete') }}"></i>
                                                    </a>
                                                    {!! Form::open(['method' => 'delete', 'url' => ['remove-from-cart'], 'id' => 'delete-form-' . $id]) !!}
                                                    <input type="hidden" name="session_key"
                                                        value="{{ $lastsegment }}">
                                                    <input type="hidden" name="id"
                                                        value="{{ $id }}">
                                                    {!! Form::close() !!}
                                                </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr class="text-center no-found">
                                            <td colspan="7">{{ __('No Data Found.!') }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="total-section mt-3">
                            <div class="sub-total">
                                <div class="d-flex text-end justify-content-end">
                                    <h6 class="mb-0 text-dark">{{ __('Sub Total') }} :</h6>
                                    <h6 class="mb-0 text-dark subtotal_price" id="displaytotal">
                                        {{ Auth::user()->priceFormat($total) }}</h6>
                                </div>
                                <div class="row align-items-center">
                                    <div class="col-6">
                                        @if(Auth::user()->can('manage discount'))
                                            <div class="d-flex text-end justify-content-end align-items-center">
                                                <span
                                                    class="input-group-text bg-transparent">{{ \Auth::user()->currencySymbol() }}</span>
                                                {{ Form::number('discount', $discount, ['class' => ' form-control discount', 'required' => 'required', 'placeholder' => __('Discount')]) }}
                                                {{ Form::hidden('discount_hidden', $discount, ['id' => 'discount_hidden']) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <h6 class="">{{ __('Sub Total') }} :</h6>
                                            <h6 class="subtotalamount">{{ Auth::user()->priceFormat($total) }}</h6>
                                        </div>
                                    </div>
                                </div>
                                <!-- Tax and Total with Tax -->
                                <div class="row align-items-center mt-2">
                                    <div class="col-6">
                                        <div class="d-flex align-items-center">
                                            <h6 class="">{{ __('ضريبة القيمة المضافة') }} :</h6>
                                            <h6 class="taxamount ms-2">
                                                @php
                                                    $tax = 0;
                                                    if(session($lastsegment) && !empty(session($lastsegment)) && count(session($lastsegment)) > 0) {
                                                        foreach(session($lastsegment) as $product) {
                                                            if(isset($product['tax'])) {
                                                                $taxRate = 0;
                                                                if(is_array($product['tax'])) {
                                                                    foreach($product['tax'] as $rate) {
                                                                        $taxRate += $rate;
                                                                    }
                                                                } else {
                                                                    $taxRate = $product['tax'];
                                                                }
                                                                // حساب الضريبة المُضمنة من السعر الشامل (subtotal)
                                                                // الضريبة = (السعر الشامل × معدل الضريبة) ÷ (100 + معدل الضريبة)
                                                                $itemSubtotal = $product['subtotal'];
                                                                $tax += ($itemSubtotal * $taxRate) / (100 + $taxRate);
                                                            }
                                                        }
                                                    }
                                                @endphp
                                                {{ Auth::user()->priceFormat($tax) }}
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <h6 class="fw-bold">{{ __('Total') }} :</h6>
                                            <h6 class="totalamount fw-bold">{{ Auth::user()->priceFormat($total) }}</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between pt-3" id="btn-pur">
                                    <button id="payment" type="button" class="btn btn-primary rounded" data-ajax-popup="true"
                                        data-size="xl" data-align="centered" data-url="{{ route('pos.billtype') }}"
                                        data-title="{{ __('POS Invoice') }}"
                                        @if (session($lastsegment) && !empty(session($lastsegment)) && count(session($lastsegment)) > 0) @else disabled="disabled" @endif>
                                        {{ __('PAY') }}
                                    </button>
                                    <div class="tab-content btn-empty text-end">

                                        <!-- New Customer Button -->
                                        <a href="{{ route('poses.index') }}" target="_blank" class="btn btn-success rounded m-0 me-2">
                                            <i class="fas fa-user-plus"></i> {{ __('New Customer') }}
                                        </a>
                                        <!-- Empty Cart Button (Modified to empty cart immediately) -->
                                        <a href="#" class="btn btn-danger rounded m-0 empty-cart-direct">
                                            <i class="fas fa-trash-alt"></i> {{ __('Empty Cart') }}
                                        </a>
                                        <input type="hidden" name="session_key" value="{{ $lastsegment }}" id="empty_cart">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>


    <div class="modal fade" id="commonModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                </div>
            </div>
        </div>
    </div>


    <div class="position-fixed top-0 end-0 p-3" style="z-index: 99999">
        <div id="liveToast" class="toast text-white  fade" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body"> </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Required Js -->
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/popper.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/perfect-scrollbar.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/dash.js') }}"></script>
    <script src="{{ asset('js/moment.min.js') }}"></script>


    <script src="{{ asset('assets/js/plugins/bootstrap-switch-button.min.js') }}"></script>

    <script src="{{ asset('assets/js/plugins/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/simple-datatables.js') }}"></script>

    <!-- Apex Chart -->
    <script src="{{ asset('assets/js/plugins/apexcharts.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/main.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/choices.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/flatpickr.min.js') }}"></script>

    <script src="{{ asset('js/jscolor.js') }}"></script>
    <script src="{{ asset('js/custom.js') }}"></script>

    @if ($message = Session::get('success'))
        <script>
            show_toastr('success', '{!! $message !!}');
        </script>
    @endif
    @if ($message = Session::get('error'))
        <script>
            show_toastr('error', '{!! $message !!}');
        </script>
    @endif
    @stack('script-page')

    <script src="{{ asset('js/jquery-ui.min.js') }}"></script>

    <script>

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $( document ).ready(function() {

            var abc = $( "#warehouse_name_hidden" ).val($('.warehouse_select').val());
            $( "#delivery_user_hidden" ).val($('.user_select').val());
            $( "#vc_name_hidden" ).val($('.customer_select').val());
            $( "#warehouse_name_hidden" ).val($('.warehouse_select').val());
            $( "#discount_hidden").val($('.discount').val());
            $( "#quotation_id").val($('.quotation').val());
            $(function () {
                getProductCategories();

                // تعطيل أزرار الدفع عند تحميل الصفحة
                checkPaymentButtonStatus();


            });

            // تحميل المنتجات الأولية عند اختيار المستودع
            // لا نحمل المنتجات عند تحميل الصفحة، بل ننتظر اختيار المستودع

            $( '#warehouse' ).change(function() {
               var ware_id = $( "#warehouse" ).val();
               var url = $('.barcodeScanner').data('url'); // الحصول على URL الصحيح

               // مسح حقل البحث عند تغيير المستودع
               $('.barcodeScanner').val('');

               // تحديث عرض المنتجات للمستودع الجديد
               if (ware_id) {
                   searchProducts(url,'','0',ware_id, 'sku');

                   // إعادة التركيز على حقل البحث
                   setTimeout(function() {
                       $('.barcodeScanner').focus();
                   }, 100);

                   // عرض رسالة توضيحية
                   show_toastr('success', '{{ __("Warehouse selected. You can now scan products!") }}', 'success');

                   // زر طباعة الفاتورة سيتم إدارته في معالج .warehouse_select
               }

               // تحديث قائمة العملاء عند تغيير المستودع بدون إعادة تحميل الصفحة
               $.ajax({
                   type: 'GET',
                   url: '{{ route("pos.index") }}',
                   data: {
                       'warehouse_id': ware_id,
                       'ajax': 1
                   },
                   dataType: 'json',
                   success: function(data) {
                       if (data.customers) {
                           // تحديث قائمة العملاء بدون إعادة تحميل الصفحة
                           var customerSelect = $('#customer');
                           customerSelect.empty();
                           customerSelect.append('<option value="">{{ __("Select Customer") }}</option>');

                           $.each(data.customers, function(id, name) {
                               customerSelect.append('<option value="' + id + '">' + name + '</option>');
                           });

                           // حفظ معرف المستودع المحدد
                           $( "#warehouse_name_hidden" ).val(ware_id);
                       }
                   }
               });
            });

            let billTypeUrl = "{{ route('pos.billtype') }}"; // Store routes in JavaScript variables
            let createUrl = "{{ route('pos.create') }}";

            $('#customer').change(function() {
                let selectedCustomer = $(this).val(); // Get selected value
                let selectedWarehouse = $('#warehouse').val(); // Get selected warehouse
                console.log(selectedCustomer);

                if (selectedCustomer && selectedWarehouse) {
                    // التحقق من توافق العميل مع المستودع المحدد
                    $.ajax({
                        type: 'GET',
                        url: '{{ route("customer.check.warehouse") }}',
                        data: {
                            'customer_id': selectedCustomer,
                            'warehouse_id': selectedWarehouse
                        },
                        success: function(warehouseData) {
                            if (!warehouseData.is_assigned) {
                                // إذا كان العميل غير متوافق مع المستودع، عرض رسالة خطأ
                                show_toastr('error', '{{ __("This customer is not assigned to the selected warehouse") }}', 'error');
                                $('#customer').val(''); // إعادة تعيين اختيار العميل
                                return;
                            }

                            // إذا كان العميل متوافق مع المستودع، التحقق من خاصية is_delivery
                            $.ajax({
                                type: 'GET',
                                url: '{{ route("customer.check.delivery") }}',
                                data: {
                                    'customer_id': selectedCustomer
                                },
                                success: function(data) {
                                    if (data.is_delivery) {
                                        $('#payment').attr('data-url', createUrl);
                                        $('#user-section').show();
                                        $('#user').attr('required', 'required');
                                    } else {
                                        $('#payment').attr('data-url', billTypeUrl);
                                        $('#user-section').hide();
                                        $( "#delivery_user_hidden" ).val('');
                                        $('#user').removeAttr('required');
                                    }

                                    // إعادة التركيز على حقل البحث بعد اختيار العميل
                                    setTimeout(function() {
                                        $('.barcodeScanner').focus();
                                    }, 100);

                                    // فحص حالة أزرار الدفع
                                    checkPaymentButtonStatus();
                                }
                            });
                        }
                    });
                } else {
                    // إذا لم يتم تحديد عميل أو مستودع
                    $('#payment').attr('data-url', billTypeUrl);
                    $('#user-section').hide();
                    $( "#delivery_user_hidden" ).val('');
                    $('#user').removeAttr('required');
                }
            });

            $( '.customer_select' ).change(function() {
                $( "#vc_name_hidden" ).val($(this).val());
            });
            $( '.user_select' ).change(function() {
                $( "#delivery_user_hidden" ).val($(this).val());
            });
            $( '.warehouse_select' ).change(function() {
                $( "#warehouse_name_hidden" ).val($(this).val());

                var session_key =  $( "#empty_cart" ).val();
                var ware_id = $(this).val();



                $.ajax({
                    type: 'POST',
                    url: '{{route('warehouse-empty-cart')}}',
                    data: {
                        'session_key': session_key
                    },
                    success: function (data) {

                        $( "#tbody" ).empty();

                        $("#tbody").html('<tr class="text-center no-found"><td colspan="7">{{__('No Data Found.!')}}</td></tr>');

                    }
                });
            });




            $(document).on('click', '#clearinput', function (e) {
                var IDs = [];
                $(this).closest('div').find("input").each(function () {
                    IDs.push('#' + this.id);
                });
                $(IDs.toString()).val('');
            });

            // تم إزالة البحث بالاسم - نستخدم فقط البحث بالباركود/SKU

            // البحث بالباركود/SKU وإضافة المنتج مباشرة للسلة (فقط عند الضغط على Enter)
            $(document).on('keyup', 'input.barcodeScanner', function(e) {
                // التحقق من اختيار المستودع قبل البحث عن المنتجات بالباركود
                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    $(this).val(''); // مسح قيمة حقل الباركود
                    return false;
                }

                var $input = $(this);
                var value = this.value.trim();

                // التحقق من الضغط على Enter فقط
                if (e.keyCode === 13) {
                    e.preventDefault(); // منع إرسال الـ form وتحديث الصفحة
                    if (value != '' && value.length >= 1) {
                        console.log('Searching for product with SKU:', value);
                        var warehouse_id = $('#warehouse').val();
                        var session_key = '{{ $lastsegment }}';

                        // البحث عن المنتج أولاً
                        $.ajax({
                            type: 'GET',
                            url: '{{ route("search.products") }}',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            data: {
                                'search': value,
                                'cat_id': '0',
                                'war_id': warehouse_id,
                                'session_key': session_key,
                                'type': 'sku'
                            },
                            success: function(searchData) {
                                console.log('Search response received');
                                // البحث عن المنتج في النتائج
                                var $searchResults = $(searchData);
                                var $productCard = $searchResults.find('.toacart').first();
                                console.log('Product card found:', $productCard.length > 0);

                                if ($productCard.length > 0) {
                                    // المنتج موجود، محاكاة النقر عليه
                                    var addToCartUrl = $productCard.data('url');
                                    console.log('Add to cart URL:', addToCartUrl);

                                    // استخدام نفس الكود المستخدم في النقر على المنتجات
                                    $.ajax({
                                        url: addToCartUrl,
                                        success: function (data) {
                                            console.log('Cart response:', data);
                                            if (data.code == '200') {

                                                $('#displaytotal').text(addCommas(data.product.subtotal));
                                                $('.subtotalamount').text(addCommas(data.product.subtotal));

                                                // حساب المجموع الفرعي والضريبة بشكل صحيح
                                                var totalTax = 0;
                                                var subtotalBeforeTax = 0;
                                                var subtotalWithTax = 0;

                                                if ('carttotal' in data) {
                                                    $.each(data.carttotal, function (key, value) {
                                                        // حساب المجموع الفرعي قبل الضريبة (السعر الأساسي × الكمية)
                                                        var itemSubtotalBeforeTax = value.price * value.quantity;
                                                        subtotalBeforeTax += itemSubtotalBeforeTax;

                                                        // حساب الضريبة المُضمنة من السعر الشامل
                                                        var itemTax = 0;
                                                        if (value.tax) {
                                                            var taxRate = 0;
                                                            if (typeof value.tax === 'object') {
                                                                $.each(value.tax, function(i, rate) {
                                                                    taxRate += parseFloat(rate);
                                                                });
                                                            } else {
                                                                taxRate = parseFloat(value.tax);
                                                            }
                                                            // حساب الضريبة المُضمنة من السعر الشامل
                                                            itemTax = (value.subtotal * taxRate) / (100 + taxRate);
                                                            totalTax += itemTax;
                                                        }

                                                        // المجموع الفرعي الشامل للضريبة
                                                        subtotalWithTax += value.subtotal;

                                                        // تحديث المجموع الفرعي للمنتج في الجدول (شامل الضريبة)
                                                        $('#product-id-' + value.id + ' .subtotal').text(addCommas(value.subtotal));
                                                        // تحديث السعر الأساسي (قبل الضريبة)
                                                        $('#product-id-' + value.id + ' .price').text(addCommas(value.price));
                                                    });

                                                    // تحديث المجاميع في أسفل الصفحة
                                                    $('#displaytotal').text(addCommas(subtotalWithTax));
                                                    $('.subtotalamount').text(addCommas(subtotalWithTax));
                                                    $('.taxamount').text(addCommas(totalTax));
                                                    $('.totalamount').text(addCommas(subtotalWithTax)); // Total = Sub Total (شامل الضريبة)

                                                    $('.discount').val('');
                                                }

                                                // تحقق مما إذا كان المنتج موجودًا بالفعل في السلة
                                                if ($('#product-id-' + data.product.id).length > 0) {
                                                    // إذا كان المنتج موجودًا، قم بتحديث الكمية فقط
                                                    $('.carttable #product-id-' + data.product.id + ' input[name="quantity"]').val(data.product.quantity);
                                                } else {
                                                    // إذا كان المنتج جديدًا، أضفه إلى السلة
                                                    $('#tbody').append(data.carthtml);
                                                    $('.no-found').addClass('d-none');
                                                }

                                                // فحص أزرار الدفع (إزالة التحديث المكرر للكمية)
                                                checkPaymentButtonStatus();
                                                $('.btn-empty button').addClass('btn-clear-cart');

                                                // مسح حقل البحث للبحث التالي
                                                $input.val('').focus();

                                                // عرض رسالة نجاح
                                                show_toastr('success', '{{ __("Product added to cart successfully!") }}', 'success');
                                            }
                                        },
                                        error: function (data) {
                                            data = data.responseJSON;
                                            show_toastr('{{ __("Error") }}', data.error, 'error');
                                            $input.val('').focus();
                                        }
                                    });
                                } else {
                                    // المنتج غير موجود
                                    show_toastr('error', '{{ __("Product not found!") }}', 'error');
                                    $input.val('').focus();
                                }
                            },
                            error: function(xhr) {
                                show_toastr('error', '{{ __("Error occurred while searching for product") }}', 'error');
                                $input.val('').focus();
                            }
                        });
                    }
                }
            });

            // منع إرسال الـ form عند الضغط على Enter في حقل البحث
            $(document).on('keydown', 'input.barcodeScanner', function(e) {
                if (e.keyCode === 13) {
                    e.preventDefault(); // منع إرسال الـ form
                    return false;
                }
            });

            function searchProducts(url, value, cat_id, war_id = '0', type) {
                // التحقق من اختيار المستودع قبل البحث عن المنتجات
                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                var session_key = '{{ $lastsegment }}';
                $.ajax({
                    type: 'GET',
                    url: url,
                    data: {
                        'search': value,
                        'cat_id': cat_id,
                        'war_id': war_id,
                        'session_key': session_key,
                        'type': type,
                    },
                    success: function(data) {
                        try {
                            // التحقق من صحة البيانات المستلمة
                            if (typeof data === 'string' && data.trim().length > 0) {
                                $('#product-listing').html(data);

                                // إصلاح مسارات الصور المكسورة
                                $('#product-listing img').each(function() {
                                    var $img = $(this);
                                    var src = $img.attr('src');

                                    // إصلاح مسارات الصور غير الصحيحة
                                    if (src && (src.includes('pro_image:') || src.endsWith('pro_image'))) {
                                        console.warn('Fixed broken image path:', src);
                                        $img.attr('src', '{{ asset("storage/uploads/pro_image/default.png") }}');
                                        $img.attr('alt', 'Product Image');
                                    }
                                });

                                // معالج أخطاء تحميل الصور
                                $('#product-listing img').on('error', function() {
                                    console.warn('Image load error, using default image');
                                    $(this).attr('src', '{{ asset("storage/uploads/pro_image/default.png") }}');
                                });

                            } else {
                                console.warn('Empty or invalid product data received');
                                $('#product-listing').html('<div class="alert alert-info">{{ __("No products found.") }}</div>');
                            }
                        } catch (e) {
                            console.error('Error processing product data:', e);
                            $('#product-listing').html('<div class="alert alert-warning">{{ __("Error processing product data.") }}</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading products:', error);
                        $('#product-listing').html('<div class="alert alert-warning">{{ __("Error loading products. Please try again.") }}</div>');
                    }
                });
            }

            function getProductCategories() {
                $.ajax({
                    type: 'GET',
                    url: '{{ route('product.categories') }}',
                    success: function (data) {
                        try {
                            // التحقق من صحة البيانات المستلمة
                            if (typeof data === 'string' && data.trim().length > 0) {
                                $('#categories-listing').html(data);
                            } else {
                                console.warn('Empty or invalid categories data received');
                                $('#categories-listing').html('<div class="alert alert-info">{{ __("No categories found.") }}</div>');
                            }
                        } catch (e) {
                            console.error('Error processing categories data:', e);
                            $('#categories-listing').html('<div class="alert alert-warning">{{ __("Error loading categories.") }}</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading categories:', error);
                        $('#categories-listing').html('<div class="alert alert-warning">{{ __("Error loading categories. Please try again.") }}</div>');
                    }
                });
            }

            $(document).on('click', '.toacart', function () {
                // التحقق من اختيار المستودع قبل إضافة المنتج إلى السلة
                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                var sum = 0;
                $.ajax({
                    url: $(this).data('url'),

                    success: function (data) {

                        if (data.code == '200') {

                            $('#displaytotal').text(addCommas(data.product.subtotal));
                            $('.subtotalamount').text(addCommas(data.product.subtotal));

                            // حساب المجموع الفرعي والضريبة بشكل صحيح
                            var totalTax = 0;
                            var subtotalBeforeTax = 0;
                            var subtotalWithTax = 0;

                            if ('carttotal' in data) {
                                $.each(data.carttotal, function (key, value) {
                                    // حساب المجموع الفرعي قبل الضريبة (السعر الأساسي × الكمية)
                                    var itemSubtotalBeforeTax = value.price * value.quantity;
                                    subtotalBeforeTax += itemSubtotalBeforeTax;

                                    // حساب الضريبة المُضمنة من السعر الشامل
                                    var itemTax = 0;
                                    if (value.tax) {
                                        var taxRate = 0;
                                        if (typeof value.tax === 'object') {
                                            $.each(value.tax, function(i, rate) {
                                                taxRate += parseFloat(rate);
                                            });
                                        } else {
                                            taxRate = parseFloat(value.tax);
                                        }
                                        // حساب الضريبة المُضمنة من السعر الشامل
                                        itemTax = (value.subtotal * taxRate) / (100 + taxRate);
                                        totalTax += itemTax;
                                    }

                                    // المجموع الفرعي الشامل للضريبة
                                    subtotalWithTax += value.subtotal;

                                    // تحديث المجموع الفرعي للمنتج في الجدول (شامل الضريبة)
                                    $('#product-id-' + value.id + ' .subtotal').text(addCommas(value.subtotal));
                                    // تحديث السعر الأساسي (قبل الضريبة)
                                    $('#product-id-' + value.id + ' .price').text(addCommas(value.price));
                                });

                                // تحديث المجاميع في أسفل الصفحة
                                $('#displaytotal').text(addCommas(subtotalWithTax));
                                $('.subtotalamount').text(addCommas(subtotalWithTax));
                                $('.taxamount').text(addCommas(totalTax));
                                $('.totalamount').text(addCommas(subtotalWithTax)); // Total = Sub Total (شامل الضريبة)

                                $('.discount').val('');
                            }

                            // تحقق مما إذا كان المنتج موجودًا بالفعل في السلة
                            if ($('#product-id-' + data.product.id).length > 0) {
                                // إذا كان المنتج موجودًا، قم بتحديث الكمية فقط
                                $('.carttable #product-id-' + data.product.id + ' input[name="quantity"]').val(data.product.quantity);
                            } else {
                                // إذا كان المنتج جديدًا، أضفه إلى السلة مع معالجة الأخطاء
                                try {
                                    if (data.carthtml && typeof data.carthtml === 'string' && data.carthtml.trim().length > 0) {
                                        // التحقق من صحة HTML قبل الإضافة
                                        var tempDiv = $('<div>').html(data.carthtml);
                                        if (tempDiv.find('tr').length > 0) {
                                            $('#tbody').append(data.carthtml);
                                            $('.no-found').addClass('d-none');
                                        } else {
                                            console.warn('Invalid cart HTML received:', data.carthtml);
                                            show_toastr('warning', '{{ __("Product added but display may be incomplete") }}', 'warning');
                                        }
                                    } else {
                                        console.warn('Empty or invalid cart HTML received');
                                        show_toastr('warning', '{{ __("Product added but display may be incomplete") }}', 'warning');
                                    }
                                } catch (e) {
                                    console.error('Error adding product to cart:', e);
                                    show_toastr('error', '{{ __("Error adding product to cart display") }}', 'error');
                                }
                            }

                            // فحص أزرار الدفع (إزالة التحديث المكرر للكمية)
                            checkPaymentButtonStatus();
                            $('.btn-empty button').addClass('btn-clear-cart');
                            // loadConfirm();
                            }
                    },
                    error: function (data) {
                        data = data.responseJSON;
                        show_toastr('{{ __("Error") }}', data.error, 'error');
                    }
                });
            });

            $(document).on('change keyup', '#carthtml input[name="quantity"]', function (e) {
                e.preventDefault();
                var ele = $(this);
                var sum = 0;
                var quantity = ele.closest('span').find('input[name="quantity"]').val();
                var discount = $('.discount').val();
                var session_key = '{{ $lastsegment }}';


                if(quantity != null && quantity != 0)
                {
                    $.ajax({
                        url: ele.data('url'),
                        method: "patch",
                        data: {
                            id: ele.attr("data-id"),
                            quantity: quantity,
                            discount: discount,
                            session_key: session_key
                        },
                        success: function (data) {

                            if (data.code == '200') {

                                if (quantity == 0) {
                                    ele.closest(".row").hide(250, function () {
                                        ele.closest(".row").remove();
                                    });
                                    // فحص حالة أزرار الدفع بعد الحذف
                                    checkPaymentButtonStatus();
                                    if ($('#tbody tr:not(.no-found)').length === 0) {
                                        $('.btn-empty button').removeClass('btn-clear-cart');
                                    }
                                }

                                var totalTax = 0;
                                var subtotalBeforeTax = 0;

                                $.each(data.product, function (key, value) {
                                    // حساب المجموع الفرعي قبل الضريبة
                                    var itemSubtotalBeforeTax = value.price * value.quantity;
                                    subtotalBeforeTax += itemSubtotalBeforeTax;

                                    // حساب الضريبة
                                    var itemTax = 0;
                                    if (value.tax) {
                                        var taxRate = 0;
                                        if (typeof value.tax === 'object') {
                                            $.each(value.tax, function(i, rate) {
                                                taxRate += parseFloat(rate);
                                            });
                                        } else {
                                            taxRate = parseFloat(value.tax);
                                        }
                                        itemTax = (itemSubtotalBeforeTax * taxRate) / 100;
                                        totalTax += itemTax;
                                    }

                                    // تحديث المجموع الفرعي للمنتج في الجدول (بعد الضريبة)
                                    var itemTotal = itemSubtotalBeforeTax + itemTax;
                                    $('#product-id-' + value.id + ' .subtotal').text(addCommas(itemTotal));
                                });

                                // تحديث المجاميع في أسفل الصفحة
                                $('#displaytotal').text(addCommas(subtotalBeforeTax));
                                $('.subtotalamount').text(addCommas(subtotalBeforeTax));
                                $('.taxamount').text(addCommas(totalTax));
                                $('.totalamount').text(addCommas(subtotalBeforeTax + totalTax));
                            }
                        },
                        error: function (data) {
                            data = data.responseJSON;
                            show_toastr('{{ __("Error") }}', data.error, 'error');
                        }
                    });
                }
            });

            // معالج حذف المنتجات المحسن - يعمل مع أيقونات السلة
            $(document).on('click', '.bs-pass-para-pos', function (e) {
                e.preventDefault();

                var formId = $(this).data('confirm-yes');
                var form = document.getElementById(formId);
                var button = $(this);

                // التحقق من وجود النموذج
                if (!form) {
                    console.error('Form not found with ID: ' + formId);
                    show_toastr('error', 'خطأ: لم يتم العثور على النموذج', 'error');
                    return;
                }

                // التحقق من أن النموذج يحتوي على معرف المنتج وليس فقط session_key
                var productIdInput = form.querySelector('input[name="id"]');
                var sessionKeyInput = form.querySelector('input[name="session_key"]');

                if (!productIdInput || !productIdInput.value) {
                    console.error('Product ID not found in form');
                    show_toastr('error', 'خطأ: معرف المنتج غير موجود', 'error');
                    return;
                }

                var productId = productIdInput.value;
                var sessionKey = sessionKeyInput ? sessionKeyInput.value : '';
                var productName = $('#product-id-' + productId + ' .name').text() || 'المنتج';

                console.log('Attempting to delete product with ID:', productId);
                console.log('Session key:', sessionKey);

                // استخدام SweetAlert للتأكيد
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: 'هل أنت متأكد؟',
                        text: "سيتم حذف " + productName + " من السلة. لا يمكن التراجع عن هذا الإجراء!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'نعم، احذف',
                        cancelButtonText: 'إلغاء',
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            performProductDeletion(productId, sessionKey, button, productName);
                        }
                    });
                } else {
                    // استخدام confirm عادي إذا لم يكن SweetAlert متوفر
                    if (confirm('هل أنت متأكد من حذف ' + productName + ' من السلة؟')) {
                        performProductDeletion(productId, sessionKey, button, productName);
                    }
                }
            });

            // دالة تنفيذ حذف المنتج
            function performProductDeletion(productId, sessionKey, button, productName) {
                // تعطيل الزر مؤقتاً لمنع النقر المتكرر
                var originalHtml = button.html();
                button.prop('disabled', true).html('<i class="ti ti-loader"></i>');

                // إرسال طلب AJAX لحذف المنتج
                $.ajax({
                    url: '{{ route("remove-from-cart") }}',
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    data: {
                        id: productId,
                        session_key: sessionKey
                    },
                    success: function(response) {
                        console.log('Product removed successfully:', response);

                        if (response.code === 200) {
                            // إزالة صف المنتج من الجدول
                            var productRow = $('#product-id-' + productId);
                            productRow.fadeOut(300, function() {
                                $(this).remove();

                                // التحقق من وجود منتجات أخرى في السلة
                                var remainingProducts = $('#tbody tr:not(.no-found)').length;

                                if (remainingProducts === 0) {
                                    // إذا لم تعد هناك منتجات، عرض رسالة "لا توجد بيانات"
                                    $('#tbody').html('<tr class="text-center no-found"><td colspan="7">{{ __("No Data Found.!") }}</td></tr>');

                                    // تعطيل أزرار الدفع
                                    $('#btn-pur button').attr('disabled', 'disabled');
                                    $('#btn-pur button').removeClass('btn-primary').addClass('btn-secondary');
                                }

                                // إعادة حساب المجاميع
                                updateCartTotalsAfterRemoval();

                                // فحص حالة أزرار الدفع
                                checkPaymentButtonStatus();
                            });

                            // عرض رسالة نجاح
                            show_toastr('success', response.success || 'تم حذف ' + productName + ' من السلة بنجاح!', 'success');
                        } else {
                            // إعادة تفعيل الزر في حالة الخطأ
                            button.prop('disabled', false).html(originalHtml);
                            show_toastr('error', response.error || 'فشل في حذف المنتج', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', xhr.responseText);

                        // إعادة تفعيل الزر في حالة الخطأ
                        button.prop('disabled', false).html(originalHtml);

                        var errorMessage = 'حدث خطأ أثناء حذف المنتج';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        }

                        show_toastr('error', errorMessage, 'error');
                    }
                });
            }

            // معالج قديم للتوافق مع الكود الآخر (إذا كان مستخدم)
            $(document).on('click', '.remove-from-cart', function (e) {
                e.preventDefault();
                console.log('Legacy remove-from-cart handler called - redirecting to new handler');

                // إعادة توجيه للمعالج الجديد إذا كان العنصر يحتوي على الكلاسات المطلوبة
                if ($(this).hasClass('bs-pass-para-pos')) {
                    return; // دع المعالج الجديد يتولى الأمر
                }

                // معالجة قديمة للعناصر الأخرى
                var ele = $(this);
                if (confirm('{{ __("Are you sure?") }}')) {
                    // كود المعالجة القديم هنا إذا لزم الأمر
                    console.log('Using legacy removal method');
                }
            });

            // Original cart clear function (kept for compatibility)
            $(document).on('click', '.btn-clear-cart', function (e) {
                e.preventDefault();
                var session_key = '{{ $lastsegment }}';

                if (confirm('{{ __("Remove all items from cart?") }}')) {
                    $.ajax({
                        url: $(this).data('url'),
                        data: {
                            session_key: session_key
                        },
                        success: function (data) {
                            location.reload();
                        },
                        error: function (data) {
                            data = data.responseJSON;
                            show_toastr('{{ __("Error") }}', data.error, 'error');
                        }
                    });
                }
            });

            // New direct empty cart function (with confirmation)
            $(document).on('click', '.empty-cart-direct', function (e) {
                e.preventDefault();
                var session_key = '{{ $lastsegment }}';

                // إضافة تأكيد لإفراغ السلة بالكامل
                if (confirm('{{ __("Are you sure you want to empty the entire cart? This will remove ALL products!") }}')) {
                    $.ajax({
                        url: '{{ route("empty-cart") }}',
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: {
                            session_key: session_key
                        },
                        success: function (data) {
                            show_toastr('success', '{{ __("Cart emptied successfully!") }}', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        },
                        error: function (data) {
                            data = data.responseJSON;
                            show_toastr('{{ __("Error") }}', data.error, 'error');
                        }
                    });
                }
            });

            // ========== IMPROVED PAYMENT BUTTON HANDLER ==========
            $(document).on('click', '.btn-done-payment', function (e) {
                e.preventDefault();
                var ele = $(this);

                // إضافة تأكيد من المستخدم قبل معالجة الدفع
                if (!confirm('{{ __("Are you sure you want to process this payment?") }}')) {
                    return false;
                }

                // التحقق من البيانات المطلوبة
                var vcName = $('#vc_name_hidden').val();
                var warehouseName = $('#warehouse_name_hidden').val();

                if (!vcName) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    return false;
                }

                if (!warehouseName) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                $.ajax({
                    url: ele.data('url'),
                    method: 'GET',
                    data: {
                        user_id: $('#delivery_user_hidden').val(),
                        vc_name: vcName,
                        warehouse_name: warehouseName,
                        discount: $('#discount_hidden').val(),
                        quotation_id: $('#quotation_id').val(),
                    },
                    beforeSend: function () {
                        // تعطيل الزر وإضافة مؤشر تحميل بدلاً من حذفه
                        ele.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> {{ __("Processing Payment...") }}');
                        // تعطيل جميع أزرار الدفع لمنع الضغط المتعدد
                        $('.btn-done-payment').prop('disabled', true);
                    },
                    success: function (data) {
                        if (data.code == 200) {
                            show_toastr('success', data.success, 'success');

                            // إخفاء جميع أزرار الدفع
                            $('.btn-done-payment').hide();

                            // زر الطباعة الحرارية متاح بالفعل عند اختيار المستودع
                            if (data.pos_id) {
                                // لا حاجة لإظهار زر الطباعة الحرارية هنا لأنه متاح بالفعل

                                // إضافة قسم خيارات ما بعد الدفع في المودال
                                var postPaymentOptions = '<div class="post-payment-success mt-3 text-center">' +
                                    '<div class="alert alert-success">' +
                                    '<h5 class="text-success mb-3"><i class="ti ti-check-circle"></i> {{ __("Payment Completed Successfully!") }}</h5>' +
                                    '<p class="mb-3">{{ __("Invoice Number") }}: <strong>' + data.pos_number + '</strong></p>' +
                                    '<div class="btn-group" role="group">' +
                                        '<a href="' + thermalPrintUrl + '" target="_blank" class="btn btn-success btn-sm me-2">' +
                                            '<i class="ti ti-device-mobile"></i> {{ __("🖨️ Thermal Print") }}</a>' +
                                        '<button type="button" class="btn btn-info btn-sm me-2" onclick="window.print()">' +
                                            '<i class="ti ti-printer"></i> {{ __("Print Preview") }}</button>' +
                                        '<button type="button" class="btn btn-secondary btn-sm" onclick="window.location.reload()">' +
                                            '<i class="ti ti-refresh"></i> {{ __("New Sale") }}</button>' +
                                    '</div>' +
                                    '</div>' +
                                '</div>';

                                // إضافة خيارات ما بعد الدفع في المودال
                                if ($('.post-payment-success').length === 0) {
                                    $('#commonModal .modal-body').append(postPaymentOptions);
                                }

                                // إضافة زر طباعة حرارية مباشر في أعلى الصفحة مع تحسينات بصرية
                                var directThermalBtn = '<div class="alert alert-success alert-dismissible fade show thermal-print-alert" role="alert" style="border-left: 5px solid #28a745; position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">' +
                                    '<div class="d-flex align-items-center">' +
                                    '<i class="ti ti-check-circle me-2" style="font-size: 1.5em; color: #28a745;"></i>' +
                                    '<div>' +
                                    '<strong>{{ __("Payment Completed Successfully!") }}</strong><br>' +
                                    '<small>{{ __("Invoice") }}: ' + data.pos_number + '</small>' +
                                    '</div>' +
                                    '</div>' +
                                    '<div class="mt-2">' +
                                    '<a href="' + thermalPrintUrl + '" target="_blank" class="btn btn-success btn-sm pulse-button">' +
                                    '<i class="ti ti-device-mobile me-1"></i> {{ __("🖨️ Print Thermal Receipt") }}</a>' +
                                    '</div>' +
                                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                    '</div>';

                                // إضافة التنبيه في أعلى الصفحة
                                if ($('.thermal-print-alert').length === 0) {
                                    $('body').prepend(directThermalBtn);

                                    // إضافة تأثير نبضة للزر
                                    setTimeout(function() {
                                        $('.pulse-button').addClass('btn-pulse');
                                    }, 500);
                                }

                                // عرض رسالة إضافية للمستخدم
                                show_toastr('success', '{{ __("Payment completed! Thermal print button is now available.") }}', 'success');
                            }

                            // إغلاق المودال بعد 5 ثوان مع عداد تنازلي
                            var countdown = 5;
                            var countdownInterval = setInterval(function() {
                                countdown--;
                                if (countdown <= 0) {
                                    clearInterval(countdownInterval);
                                    $('#commonModal').modal('hide');

                                    // تفريغ السلة وإعادة تحميل الصفحة
                                    setTimeout(function () {
                                        $.ajax({
                                            url: '{{route('empty-cart')}}',
                                            type: 'POST',
                                            data: {
                                                session_key: '{{ $lastsegment }}'
                                            },
                                            success: function() {
                                                // إعادة تحميل الصفحة مباشرة
                                                setTimeout(function() {
                                                    window.location.reload();
                                                }, 1000);
                                            }
                                        });
                                    }, 1000);
                                } else {
                                    $('.post-payment-success h5').html('<i class="ti ti-check-circle"></i> {{ __("Payment Completed Successfully!") }} <small class="text-muted">({{ __("Auto close in") }} ' + countdown + 's)</small>');
                                }
                            }, 1000);
                        } else {
                            // في حالة فشل الدفع، إعادة تفعيل الأزرار
                            show_toastr('error', data.error || '{{ __("Payment failed") }}', 'error');
                            ele.prop('disabled', false).html('{{ __("Cash Payment") }}');
                            $('.btn-done-payment').prop('disabled', false);
                        }
                    },
                    error: function (xhr) {
                        var data = xhr.responseJSON;
                        show_toastr('error', data.error || '{{ __("An error occurred during payment processing") }}', 'error');

                        // إعادة تفعيل الأزرار في حالة الخطأ
                        ele.prop('disabled', false).html('{{ __("Cash Payment") }}');
                        $('.btn-done-payment').prop('disabled', false);
                    }
                });
            });



            // معالج النقر لزر الطباعة الحرارية في التنبيه
            $(document).on('click', '.thermal-print-alert a', function(e) {
                e.preventDefault();
                var printUrl = $(this).attr('href');

                if (confirm('{{ __("هل تريد طباعة الفاتورة الحرارية؟") }}')) {
                    var printWindow = window.open(printUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');

                    if (printWindow) {
                        show_toastr('success', '{{ __("تم فتح نافذة الطباعة الحرارية") }}', 'success');

                        // إخفاء التنبيه بعد الاستخدام
                        setTimeout(function() {
                            $('.thermal-print-alert').fadeOut();
                        }, 2000);
                    } else {
                        show_toastr('error', '{{ __("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.") }}', 'error');
                    }
                }
            });

            $(document).on('click', '.category-select', function (e) {
                // التحقق من اختيار المستودع قبل عرض منتجات الفئة
                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                var cat = $(this).data('cat-id');
                var white = 'text-white';
                var dark = 'text-dark';
                $('.category-select').parent().removeClass('cat-active');
                $('.category-select').find('.card-title').removeClass('text-white').addClass('text-dark');
                $('.category-select').find('.card-title').parent().removeClass('text-white').addClass('text-dark');
                $(this).find('.card-title').removeClass('text-dark').addClass('text-white');
                $(this).find('.card-title').parent().removeClass('text-dark').addClass('text-white');
                $(this).parent().addClass('cat-active');
                var url = '{{ route('search.products') }}'

                var warehouse_id=$('#warehouse').val();
                searchProducts(url,'',cat,warehouse_id, 'sku'); // هنا نحتاج فحص العميل لأن المستخدم يتفاعل مع الفئات

            });

            // دالة تحديث مجاميع السلة
            function updateCartTotals(carttotal) {
                if (carttotal) {
                    var totalTax = 0;
                    var subtotalBeforeTax = 0;
                    var subtotalWithTax = 0;

                    $.each(carttotal, function (key, value) {
                        // حساب المجموع الفرعي قبل الضريبة (السعر الأساسي × الكمية)
                        var itemSubtotalBeforeTax = value.price * value.quantity;
                        subtotalBeforeTax += itemSubtotalBeforeTax;

                        // حساب الضريبة المُضمنة من السعر الشامل
                        var itemTax = 0;
                        if (value.tax) {
                            var taxRate = 0;
                            if (typeof value.tax === 'object') {
                                $.each(value.tax, function(i, rate) {
                                    taxRate += parseFloat(rate);
                                });
                            } else {
                                taxRate = parseFloat(value.tax);
                            }
                            // حساب الضريبة المُضمنة من السعر الشامل
                            itemTax = (value.subtotal * taxRate) / (100 + taxRate);
                            totalTax += itemTax;
                        }

                        // المجموع الفرعي الشامل للضريبة
                        subtotalWithTax += value.subtotal;

                        // تحديث المجموع الفرعي للمنتج في الجدول (شامل الضريبة)
                        $('#product-id-' + value.id + ' .subtotal').text(addCommas(value.subtotal));
                        // تحديث السعر الأساسي (قبل الضريبة)
                        $('#product-id-' + value.id + ' .price').text(addCommas(value.price));
                    });

                    // تحديث المجاميع في أسفل الصفحة
                    $('#displaytotal').text(addCommas(subtotalWithTax));
                    $('.subtotalamount').text(addCommas(subtotalWithTax));
                    $('.taxamount').text(addCommas(totalTax));
                    $('.totalamount').text(addCommas(subtotalWithTax)); // Total = Sub Total (شامل الضريبة)
                }
            }

            // دالة إضافة الفواصل للأرقام
            function addCommas(num) {
                return parseFloat(num).toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }

            // دالة محسنة لإعادة حساب المجاميع بعد حذف منتج من السلة
            function updateCartTotalsAfterRemoval() {
                var subtotal = 0;
                var totalTax = 0;

                // حساب المجاميع من المنتجات المتبقية في الجدول
                $('#tbody tr:not(.no-found)').each(function() {
                    var row = $(this);
                    var quantity = parseFloat(row.find('input[name="quantity"]').val()) || 0;

                    // الحصول على السعر من العمود المخصص له
                    var priceText = row.find('.price').text().replace(/[^\d.-]/g, '');
                    var price = parseFloat(priceText) || 0;

                    // الحصول على المجموع الفرعي من العمود المخصص له
                    var subtotalText = row.find('.subtotal').text().replace(/[^\d.-]/g, '');
                    var itemSubtotal = parseFloat(subtotalText) || 0;

                    subtotal += itemSubtotal;

                    // حساب الضريبة المُضمنة من السعر الشامل
                    // افتراض أن الضريبة 15% مُضمنة في السعر
                    var taxRate = 15;
                    var itemTax = (itemSubtotal * taxRate) / (100 + taxRate);
                    totalTax += itemTax;
                });

                // تحديث المجاميع في الواجهة
                $('.subtotalamount').text(addCommas(subtotal.toFixed(2)));
                $('.taxamount').text(addCommas(totalTax.toFixed(2)));
                $('.totalamount').text(addCommas(subtotal.toFixed(2))); // المجموع = المجموع الفرعي (شامل الضريبة)
                $('#displaytotal').text(addCommas(subtotal.toFixed(2)));

                // إذا لم تعد هناك منتجات، تصفير المجاميع
                if (subtotal === 0) {
                    $('.subtotalamount').text('0.00');
                    $('.taxamount').text('0.00');
                    $('.totalamount').text('0.00');
                    $('#displaytotal').text('0.00');
                }

                console.log('Cart totals updated:', {
                    subtotal: subtotal,
                    tax: totalTax,
                    total: subtotal
                });
            }

            // دالة فحص حالة أزرار الدفع
            function checkPaymentButtonStatus() {
                var selectedCustomer = $('#customer').val();
                var hasItemsInCart = $('#tbody tr:not(.no-found)').length > 0;

                if (selectedCustomer && hasItemsInCart) {
                    // تفعيل أزرار الدفع
                    $('#btn-pur button').removeAttr('disabled');
                    $('#btn-pur button').removeClass('btn-secondary').addClass('btn-primary');
                } else {
                    // تعطيل أزرار الدفع
                    $('#btn-pur button').attr('disabled', 'disabled');
                    $('#btn-pur button').removeClass('btn-primary').addClass('btn-secondary');

                    if (!selectedCustomer && hasItemsInCart) {
                        show_toastr('warning', '{{ __("Please select a customer to proceed with payment") }}', 'warning');
                    }
                }
            }
            $(document).on('keyup', '.discount', function () {


                var discount = $('.discount').val();

                $( "#discount_hidden" ).val(discount);
                $.ajax({
                    url: "{{route('cartdiscount')}}",
                    method: 'POST',
                    data: {discount: discount,},

                    success: function (data) {

                        $('.totalamount').text(data.total);

                    },
                    error: function (data) {
                        data = data.responseJSON;
                        show_toastr('{{ __("Error") }}', data.error, 'error');
                    }
                });


                {{--var price = {{$total}}--}}
                {{--    var total_amount = price-discount;--}}
                {{--    $('.totalamount').text(total_amount);--}}



            })





        });




    </script>
    <script>
        // تعريف المتغيرات العامة
        var site_currency_symbol_position = '{{ \App\Models\Utility::getValByName('site_currency_symbol_position') }}';
        var site_currency_symbol = '{{ \App\Models\Utility::getValByName('site_currency_symbol') }}';

        // إضافة معالج أخطاء عام للـ AJAX
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.error('AJAX Error:', {
                url: settings.url,
                status: xhr.status,
                error: thrownError,
                response: xhr.responseText
            });

            // تجنب عرض أخطاء غير مهمة
            if (xhr.status !== 0 && xhr.status !== 200) {
                show_toastr('error', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            }
        });

        // حل محسن: اعتراض AJAX مع استثناءات للعمليات المشروعة
        (function() {
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                const originalSuccess = options.success;
                const originalDataFilter = options.dataFilter;

                // فحص إذا كان الطلب متعلق بالمنتجات أو السلة (عمليات مشروعة)
                const isProductRequest = options.url && (
                    options.url.includes('search.products') ||
                    options.url.includes('add-to-cart') ||
                    options.url.includes('product.categories') ||
                    options.url.includes('update-cart') ||
                    options.url.includes('remove-from-cart')
                );

                // تطبيق التنظيف فقط على الطلبات غير المشروعة
                if (!isProductRequest) {
                    options.dataFilter = function(data, type) {
                        if (typeof data === 'string') {
                            // إزالة script tags المشكوك فيها فقط
                            if (data.includes('slideUp') || data.includes('slideDown') || data.includes('FullCalendar')) {
                                data = data.replace(/<script[^>]*>[\s\S]*?(slideUp|slideDown|FullCalendar)[\s\S]*?<\/script>/gi, '<!-- Conflicting script removed -->');
                                console.log('AJAX response cleaned from conflicting JavaScript');
                            }
                        }

                        // تطبيق dataFilter الأصلي إن وجد
                        if (originalDataFilter) {
                            data = originalDataFilter.call(this, data, type);
                        }

                        return data;
                    };
                }

                // تنظيف محدود للـ success callback
                options.success = function(data, textStatus, jqXHR) {
                    // تنظيف فقط للطلبات غير المشروعة
                    if (!isProductRequest && typeof data === 'string') {
                        if (data.includes('slideUp') || data.includes('slideDown')) {
                            data = data.replace(/slideUp|slideDown/gi, 'BLOCKED');
                        }
                    }

                    if (originalSuccess) {
                        originalSuccess.call(this, data, textStatus, jqXHR);
                    }
                };

                return originalAjax.call(this, options);
            };
        })();

        // حماية محسنة: اعتراض jQuery.html() مع استثناءات للعمليات المشروعة
        (function() {
            if (typeof $ !== 'undefined' && $.fn.html) {
                const originalHtml = $.fn.html;
                $.fn.html = function(value) {
                    if (arguments.length > 0 && typeof value === 'string') {
                        // فحص إذا كان العنصر متعلق بالمنتجات أو السلة
                        const isProductElement = this.is('#product-listing, #categories-listing, #tbody, .carttable') ||
                                               this.closest('#product-listing, #categories-listing, #tbody, .carttable').length > 0;

                        // تطبيق التنظيف فقط على العناصر غير المشروعة
                        if (!isProductElement) {
                            // تنظيف محدود للكود المشكوك فيه فقط
                            if (value.includes('slideUp') || value.includes('slideDown') || value.includes('FullCalendar')) {
                                value = value.replace(/<script[^>]*>[\s\S]*?(slideUp|slideDown|FullCalendar)[\s\S]*?<\/script>/gi, '<!-- Conflicting script removed -->');
                                value = value.replace(/slideUp|slideDown|FullCalendar/gi, 'BLOCKED');
                                console.warn('Blocked HTML content containing conflicting JavaScript');
                            }
                        }
                    }
                    return originalHtml.call(this, value);
                };
            }
        })();

        // إضافة معالج أخطاء عام للـ JavaScript
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });

            // تجنب إظهار أخطاء slideUp المكررة
            if (e.message.includes('slideUp') && e.message.includes('already been declared')) {
                e.preventDefault();
                return false;
            }
        });

        // تجنب تحميل FullCalendar المتعدد بحماية قوية
        (function() {
            if (typeof FullCalendar !== 'undefined') {
                if (window.fullCalendarLoaded) {
                    console.warn('FullCalendar already loaded, preventing duplicate initialization');
                    // منع إعادة تهيئة FullCalendar
                    const originalCalendar = FullCalendar.Calendar;
                    FullCalendar.Calendar = function() {
                        console.warn('Blocked duplicate FullCalendar initialization');
                        return {
                            render: function() { console.warn('FullCalendar render blocked'); },
                            destroy: function() { console.warn('FullCalendar destroy blocked'); }
                        };
                    };
                } else {
                    window.fullCalendarLoaded = true;
                    console.log('FullCalendar marked as loaded');
                }
            }
        })();

        // منع إعادة تعريف دوال slideUp/slideDown بحماية قوية
        (function() {
            // تعريف الدوال مرة واحدة فقط وحمايتها من إعادة التعريف
            if (typeof window.slideUp === 'undefined') {
                Object.defineProperty(window, 'slideUp', {
                    value: function(target, duration = 0) {
                        if (!target) return;
                        target.style.transitionProperty = "height, margin, padding";
                        target.style.transitionDuration = duration + "ms";
                        target.style.boxSizing = "border-box";
                        target.style.height = target.offsetHeight + "px";
                        target.offsetHeight;
                        target.style.overflow = "hidden";
                        target.style.height = 0;
                        target.style.paddingTop = 0;
                        target.style.paddingBottom = 0;
                        target.style.marginTop = 0;
                        target.style.marginBottom = 0;
                    },
                    writable: false,
                    configurable: false
                });
            }
        })();

        (function() {
            // حماية دالة slideDown من إعادة التعريف
            if (typeof window.slideDown === 'undefined') {
                Object.defineProperty(window, 'slideDown', {
                    value: function(target, duration = 0) {
                        if (!target) return;
                        target.style.removeProperty("display");
                        let display = window.getComputedStyle(target).display;
                        if (display === "none") display = "block";
                        target.style.display = display;
                        let height = target.offsetHeight;
                        target.style.overflow = "hidden";
                        target.style.height = 0;
                        target.style.paddingTop = 0;
                        target.style.paddingBottom = 0;
                        target.style.marginTop = 0;
                        target.style.marginBottom = 0;
                        target.offsetHeight;
                        target.style.boxSizing = "border-box";
                        target.style.transitionProperty = "height, margin, padding";
                        target.style.transitionDuration = duration + "ms";
                        target.style.height = height + "px";
                        target.style.removeProperty("padding-top");
                        target.style.removeProperty("padding-bottom");
                        target.style.removeProperty("margin-top");
                        target.style.removeProperty("margin-bottom");
                        window.setTimeout(() => {
                            target.style.removeProperty("height");
                            target.style.removeProperty("overflow");
                            target.style.removeProperty("transition-duration");
                            target.style.removeProperty("transition-property");
                        }, duration);
                    },
                    writable: false,
                    configurable: false
                });
            }
        })();

        // حماية شاملة: منع تنفيذ أي JavaScript يحتوي على slideUp/slideDown
        (function() {
            // اعتراض window.eval
            const originalEval = window.eval;
            window.eval = function(code) {
                if (typeof code === 'string') {
                    // منع تنفيذ أي كود يحتوي على slideUp أو slideDown
                    if (code.includes('slideUp') || code.includes('slideDown') ||
                        code.includes('FullCalendar')) {
                        console.warn('Blocked JavaScript execution containing slideUp/slideDown/FullCalendar');
                        return;
                    }
                }
                return originalEval.call(this, code);
            };

            // اعتراض jQuery's globalEval
            if (typeof $ !== 'undefined' && $.globalEval) {
                const originalGlobalEval = $.globalEval;
                $.globalEval = function(code) {
                    if (typeof code === 'string') {
                        if (code.includes('slideUp') || code.includes('slideDown') ||
                            code.includes('FullCalendar')) {
                            console.warn('Blocked jQuery.globalEval containing slideUp/slideDown/FullCalendar');
                            return;
                        }
                    }
                    return originalGlobalEval.call(this, code);
                };
            }

            // اعتراض createElement للـ script tags
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(this, tagName);
                if (tagName.toLowerCase() === 'script') {
                    const originalSetAttribute = element.setAttribute;
                    element.setAttribute = function(name, value) {
                        if (name === 'src' && typeof value === 'string') {
                            if (value.includes('dash.js') || value.includes('slideUp')) {
                                console.warn('Blocked script loading:', value);
                                return;
                            }
                        }
                        return originalSetAttribute.call(this, name, value);
                    };

                    Object.defineProperty(element, 'innerHTML', {
                        set: function(value) {
                            if (typeof value === 'string' &&
                                (value.includes('slideUp') || value.includes('slideDown'))) {
                                console.warn('Blocked script innerHTML containing slideUp/slideDown');
                                return;
                            }
                            this.textContent = value;
                        }
                    });
                }
                return element;
            };

            // حماية محسنة: اعتراض appendChild للـ script elements المشكوك فيها فقط
            const originalAppendChild = Node.prototype.appendChild;
            Node.prototype.appendChild = function(child) {
                // فحص script elements فقط
                if (child.tagName && child.tagName.toLowerCase() === 'script') {
                    const scriptContent = child.innerHTML || child.textContent || '';
                    // منع فقط الـ scripts التي تحتوي على الكود المشكوك فيه
                    if (scriptContent.includes('slideUp') || scriptContent.includes('slideDown') ||
                        scriptContent.includes('FullCalendar')) {
                        console.warn('Blocked appendChild of script containing conflicting JavaScript');
                        return child; // إرجاع العنصر بدون إضافته
                    }
                }
                return originalAppendChild.call(this, child);
            };

            // حماية insertBefore للـ scripts المشكوك فيها فقط
            const originalInsertBefore = Node.prototype.insertBefore;
            Node.prototype.insertBefore = function(newNode, referenceNode) {
                if (newNode.tagName && newNode.tagName.toLowerCase() === 'script') {
                    const scriptContent = newNode.innerHTML || newNode.textContent || '';
                    if (scriptContent.includes('slideUp') || scriptContent.includes('slideDown') ||
                        scriptContent.includes('FullCalendar')) {
                        console.warn('Blocked insertBefore of script containing conflicting JavaScript');
                        return newNode;
                    }
                }
                return originalInsertBefore.call(this, newNode, referenceNode);
            };
        })();

        // إصلاح مشكلة aria-hidden في المودال
        (function() {
            // معالج فتح المودال
            $('#commonModal').on('show.bs.modal', function() {
                // إزالة aria-hidden عند فتح المودال
                $(this).removeAttr('aria-hidden');
                console.log('Modal opened: aria-hidden removed');
            });

            // معالج إغلاق المودال
            $('#commonModal').on('hide.bs.modal', function() {
                // إضافة aria-hidden عند إغلاق المودال
                $(this).attr('aria-hidden', 'true');
                console.log('Modal closed: aria-hidden added');
            });

            // معالج إضافي للتأكد من إدارة focus بشكل صحيح
            $('#commonModal').on('shown.bs.modal', function() {
                // التأكد من أن المودال مرئي للتقنيات المساعدة
                $(this).removeAttr('aria-hidden');

                // تركيز على أول عنصر قابل للتفاعل في المودال
                var $focusableElements = $(this).find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                if ($focusableElements.length > 0) {
                    $focusableElements.first().focus();
                }
            });

            // معالج إضافي عند الإغلاق الكامل
            $('#commonModal').on('hidden.bs.modal', function() {
                // التأكد من إضافة aria-hidden
                $(this).attr('aria-hidden', 'true');

                // مسح محتوى المودال
                $(this).find('.modal-body').empty();
                $(this).find('.modal-title').empty();

                // إعادة التركيز للعنصر الذي فتح المودال (إن أمكن)
                if (window.lastModalTrigger && $(window.lastModalTrigger).length > 0) {
                    $(window.lastModalTrigger).focus();
                }
            });

            // حفظ العنصر الذي فتح المودال لإعادة التركيز إليه لاحقاً
            $(document).on('click', '[data-ajax-popup="true"], [data-bs-toggle="modal"]', function() {
                window.lastModalTrigger = this;
            });
        })();
    </script>

    <!-- CSS للتأثيرات البصرية -->
    <style>
        /* تأثير النبضة للأزرار */
        .btn-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(23, 162, 184, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(23, 162, 184, 0);
            }
        }



        /* تحسين مظهر التنبيهات */
        .thermal-print-alert {
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسين مظهر أزرار الدفع */
        .btn-done-payment:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* تحسين مظهر المودال */
        .post-payment-success .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.2);
        }

        /* تحسين مظهر أزرار المجموعة */
        .btn-group .btn {
            border-radius: 5px !important;
            margin: 0 2px;
        }

        /* تأثير التحويم على الأزرار */
        .btn:hover {
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }
    </style>
</body>

</html>
